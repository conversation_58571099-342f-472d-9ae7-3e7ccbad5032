<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Login - Web2Img Dashboard
    </title>
    <script src="https://cdn.tailwindcss.com">
      
    </script>
  </head>
  <body class="bg-gray-100 min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Web2Img Dashboard
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            Sign in to your account
          </p>
          <p class="mt-1 text-center text-xs text-gray-500">
            Need an account? Contact your administrator
          </p>
        </div>

        <!-- Login Form -->
        <div class="bg-white rounded-lg shadow-md p-6" id="login-form">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Sign In
          </h3>
          <form action="/auth/login" method="POST" class="space-y-4">
            <div>
              <label for="login-email" class="block text-sm font-medium text-gray-700">Email</label>
              <input
                type="email"
                id="login-email"
                name="email"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <div>
              <label for="login-password" class="block text-sm font-medium text-gray-700">Password</label>
              <input
                type="password"
                id="login-password"
                name="password"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <button
              type="submit"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Sign In
                    </button>
          </form>
        </div>

        <!-- Error/Success Messages -->
        <div id="message-container" class="hidden">
          <div class="rounded-md p-4" id="message">
            <div class="text-sm" id="message-text">
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      const urlParams = new URLSearchParams(window.location.search);
      
      const error = urlParams.get("error");
      
      const success = urlParams.get("success");
      
      if (error || success) {
        const messageContainer = document.getElementById("message-container");
        const message = document.getElementById("message");
        const messageText = document.getElementById("message-text");
        messageContainer.classList.remove("hidden");
        if (error) {
          message.className = "rounded-md p-4 bg-red-50 border border-red-200";
          messageText.className = "text-sm text-red-800";
          messageText.textContent = error;
        } else if (success) {
          message.className = "rounded-md p-4 bg-green-50 border border-green-200";
          messageText.className = "text-sm text-green-800";
          messageText.textContent = success;
        }
      }
    </script>
  </body>
</html>
