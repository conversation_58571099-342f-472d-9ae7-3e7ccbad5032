<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title || 'Web2Img Dashboard' }}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .status-healthy {
            @apply bg-green-100 text-green-800;
        }

        .status-degraded {
            @apply bg-yellow-100 text-yellow-800;
        }

        .status-unhealthy {
            @apply bg-red-100 text-red-800;
        }

        .metric-card {
            @apply bg-white rounded-lg shadow p-6;
        }

        .chart-container {
            @apply bg-white rounded-lg shadow p-6;
        }
    </style>
    @if($slots.head)
    {{{ await $slots.head() }}}
    @end
</head>

<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center">
                    <h1 class="text-3xl font-bold text-gray-900">{{ header || 'Web2Img Dashboard' }}</h1>

                    <!-- User menu -->
                    <div class="relative">
                        <div class="flex items-center space-x-4">
                            <!-- User info -->
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium" id="user-initials">U</span>
                                </div>
                                <div class="hidden md:block">
                                    <div class="text-sm font-medium text-gray-900" id="user-name">Loading...</div>
                                    <div class="text-xs text-gray-500" id="user-email">Loading...</div>
                                </div>
                            </div>

                            <!-- Action buttons -->
                            <div class="flex items-center space-x-2">
                                <a href="/docs" target="_blank"
                                    class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium inline-flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                        </path>
                                    </svg>
                                    API Docs
                                </a>
                                <button onclick="showChangePasswordModal()"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                                    Change Password
                                </button>
                                <form action="/dashboard/logout" method="POST" class="inline">
                                    <button type="submit"
                                        class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation bar -->
        <nav class="bg-gray-50 border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex space-x-8 py-3">
                    <a href="/dashboard"
                        class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium border-b-2 border-blue-600">
                        Dashboard
                    </a>
                    <a href="/docs" target="_blank"
                        class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium border-b-2 border-transparent hover:border-blue-600 inline-flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        API Documentation
                    </a>
                    <a href="/health" target="_blank"
                        class="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium border-b-2 border-transparent hover:border-blue-600 inline-flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        System Health
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            {{{ await $slots.main() }}}
        </main>
    </div>

    @if($slots.scripts)
    {{{ await $slots.scripts() }}}
    @end
</body>

</html>