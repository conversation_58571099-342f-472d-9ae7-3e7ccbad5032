<!-- API Key Management -->
<div class="bg-white overflow-hidden shadow rounded-lg mb-6">
  <div class="px-4 py-5 sm:p-6">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg leading-6 font-medium text-gray-900">
        API Key Management
      </h3>
      <button
        onclick="showCreateApiKeyModal()"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
      >
        Create New API Key
            </button>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Name
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Key
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Rate Limit
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody id="api-keys-table" class="bg-white divide-y divide-gray-200">
          <!-- API keys will be loaded here -->
        </tbody>
      </table>
    </div>
  </div>
</div>
