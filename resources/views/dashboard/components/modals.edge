<!-- Create API Key Modal -->
<div id="create-api-key-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 mb-4">
        Create New API Key
      </h3>
      <form id="create-api-key-form">
        <div class="mb-4">
          <label for="api-key-name" class="block text-sm font-medium text-gray-700">Name</label>
          <input type="text" id="api-key-name" name="name" required
            class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
        </div>
        <div class="mb-4">
          <label for="api-key-rate-limit" class="block text-sm font-medium text-gray-700">Rate Limit
            (requests/hour)</label>
          <input type="number" id="api-key-rate-limit" name="rateLimit" value="1000" min="1" max="10000"
            class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
        </div>
        <div class="flex justify-end space-x-3">
          <button type="button" onclick="hideCreateApiKeyModal()"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium">
            Cancel
          </button>
          <button type="submit"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            Create
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- New API Key Display Modal -->
<div id="new-api-key-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 mb-4">
        API Key Created Successfully
      </h3>
      <div class="mb-4">
        <p class="text-sm text-gray-600 mb-2">
          Your new API key:
        </p>
        <div class="bg-gray-100 p-3 rounded border">
          <code id="new-api-key-value" class="text-sm break-all"></code>
        </div>
        <p class="text-xs text-red-600 mt-2">
          ⚠️ Save this key now - you won't be able to see it again!
        </p>
      </div>
      <div class="flex justify-end">
        <button onclick="hideNewApiKeyModal()"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Create User Modal -->
<div id="new-user-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">
      Create New User
    </h3>

    <form id="create-user-form" class="space-y-4">
      <div>
        <label for="user-name" class="block text-sm font-medium text-gray-700">Full Name</label>
        <input type="text" id="user-name" name="fullName" required
          class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
      </div>

      <div>
        <label for="user-email" class="block text-sm font-medium text-gray-700">Email</label>
        <input type="email" id="user-email" name="email" required
          class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
      </div>

      <div>
        <label for="user-password" class="block text-sm font-medium text-gray-700">Password</label>
        <input type="password" id="user-password" name="password" required minlength="6"
          class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
        <p class="mt-1 text-xs text-gray-500">
          Minimum 6 characters
        </p>
      </div>

      <div class="flex space-x-3">
        <button type="submit"
          class="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
          Create User
        </button>
        <button type="button" onclick="hideNewUserModal()"
          class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium">
          Cancel
        </button>
      </div>
    </form>

    <div id="user-creation-success" class="hidden mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
      User created successfully!
    </div>
  </div>
</div>

<!-- Change Password Modal -->
<div id="change-password-modal"
  class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">
      Change Password
    </h3>

    <form id="change-password-form" class="space-y-4">
      <div>
        <label for="current-password" class="block text-sm font-medium text-gray-700">Current Password</label>
        <input type="password" id="current-password" name="currentPassword" required
          class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
      </div>

      <div>
        <label for="new-password" class="block text-sm font-medium text-gray-700">New Password</label>
        <input type="password" id="new-password" name="newPassword" required minlength="6"
          class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
        <p class="mt-1 text-xs text-gray-500">
          Minimum 6 characters
        </p>
      </div>

      <div>
        <label for="confirm-password" class="block text-sm font-medium text-gray-700">Confirm New
          Password</label>
        <input type="password" id="confirm-password" name="confirmPassword" required minlength="6"
          class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" />
      </div>

      <div class="flex space-x-3">
        <button type="submit"
          class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
          Change Password
        </button>
        <button type="button" onclick="hideChangePasswordModal()"
          class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium">
          Cancel
        </button>
      </div>
    </form>

    <div id="password-change-success"
      class="hidden mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
      Password changed successfully!
    </div>

    <div id="password-change-error" class="hidden mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
      <span id="password-error-text"></span>
    </div>
  </div>
</div>

<!-- API Usage Details Modal -->
<div id="api-usage-details-modal"
  class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">
          API Usage Details
        </h3>
        <button onclick="hideApiUsageDetailsModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Loading State -->
      <div id="usage-details-loading" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading usage details...</p>
      </div>

      <!-- Content -->
      <div id="usage-details-content" class="hidden">
        <!-- API Key Info -->
        <div class="bg-gray-50 p-4 rounded-lg mb-6">
          <h4 class="text-md font-medium text-gray-900 mb-2">API Key Information</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <span class="text-sm text-gray-500">Name:</span>
              <p class="font-medium" id="details-api-key-name">-</p>
            </div>
            <div>
              <span class="text-sm text-gray-500">User:</span>
              <p class="font-medium" id="details-api-key-user">-</p>
            </div>
            <div>
              <span class="text-sm text-gray-500">Rate Limit:</span>
              <p class="font-medium" id="details-api-key-rate-limit">-</p>
            </div>
          </div>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div class="bg-blue-50 p-4 rounded-lg">
            <h5 class="text-sm font-medium text-blue-900">Total Requests</h5>
            <p class="text-2xl font-bold text-blue-600" id="details-total-requests">0</p>
          </div>
          <div class="bg-green-50 p-4 rounded-lg">
            <h5 class="text-sm font-medium text-green-900">Successful</h5>
            <p class="text-2xl font-bold text-green-600" id="details-successful-requests">0</p>
          </div>
          <div class="bg-red-50 p-4 rounded-lg">
            <h5 class="text-sm font-medium text-red-900">Errors</h5>
            <p class="text-2xl font-bold text-red-600" id="details-error-requests">0</p>
          </div>
          <div class="bg-yellow-50 p-4 rounded-lg">
            <h5 class="text-sm font-medium text-yellow-900">Avg Response Time</h5>
            <p class="text-2xl font-bold text-yellow-600" id="details-avg-response-time">0ms</p>
          </div>
        </div>

        <!-- Endpoint Statistics -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-900 mb-3">Endpoint Statistics</h4>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Endpoint
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requests
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Errors</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Error Rate
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg
                    Response</th>
                </tr>
              </thead>
              <tbody id="details-endpoint-stats" class="bg-white divide-y divide-gray-200">
                <!-- Endpoint stats will be populated here -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Recent Usage -->
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Recent Usage (Last 20 requests)</h4>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Endpoint
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Response
                    Time</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address
                  </th>
                </tr>
              </thead>
              <tbody id="details-recent-usage" class="bg-white divide-y divide-gray-200">
                <!-- Recent usage will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div id="usage-details-error" class="hidden text-center py-8">
        <div class="text-red-600 mb-2">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-gray-600" id="usage-details-error-message">Failed to load usage details</p>
        <button onclick="hideApiUsageDetailsModal()"
          class="mt-4 bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium">
          Close
        </button>
      </div>
    </div>
  </div>
</div>