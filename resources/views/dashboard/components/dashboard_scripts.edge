<script>
  // Dashboard JavaScript functionality
  let dashboardData = {};

  let usageData = {};

  let errorData = {};

  let apiKeysData = {};
  // Initialize dashboard

  document.addEventListener("DOMContentLoaded", function () {
    loadDashboardData();
    loadUsageOverview();
    loadErrorLogs();
    loadApiKeys();
    loadUsers();
    loadCurrentUser();
    // Set up event listeners
    document.getElementById("usage-timeframe").addEventListener("change", loadUsageOverview);
    document.getElementById("error-level").addEventListener("change", loadErrorLogs);
    document.getElementById("create-api-key-form").addEventListener("submit", createApiKey);
    document.getElementById("create-user-form").addEventListener("submit", createUser);
    document.getElementById("change-password-form").addEventListener("submit", changePassword);
    // Auto-refresh every 30 seconds
    setInterval(() => {
      loadDashboardData();
      loadUsageOverview();
      loadErrorLogs();
      loadApiKeys();
      loadUsers();
    }, 3e4);
  });
  // Load main dashboard data

  async function loadDashboardData() {
    try {
      const response = await fetch("/dashboard/api/data");
      if (!response.ok) throw new Error("Failed to fetch dashboard data");
      dashboardData = await response.json();
      updateDashboardUI();
      document.getElementById("loading").classList.add("hidden");
      document.getElementById("dashboard-content").classList.remove("hidden");
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      showError("Failed to load dashboard data");
    }
  }
  // Update dashboard UI with data

  function updateDashboardUI() {
    // System status
    const statusElement = document.getElementById("system-status");
    const statusText = document.getElementById("status-text");
    statusText.textContent = dashboardData.health.status;
    statusElement.className = `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium status-${dashboardData.health.status}`;
    document.getElementById("uptime").textContent = Math.round(dashboardData.health.uptime / 3600);
    document.getElementById("total-api-keys").textContent = dashboardData.stats.totalApiKeys;
    document.getElementById("active-api-keys").textContent = dashboardData.stats.activeApiKeys;
    // Request metrics
    document.getElementById("total-requests").textContent = dashboardData.metrics.requests.totalRequests || 0;
    document.getElementById("requests-per-sec").textContent = (dashboardData.metrics.requests.requestsPerSecond || 0).toFixed(2);
    document.getElementById("avg-response-time").textContent = `${dashboardData.metrics.requests.avgResponseTime || 0}ms`;
    document.getElementById("error-rate").textContent = `${(dashboardData.metrics.requests.errorRate || 0).toFixed(2)}%`;
    // Processing metrics
    document.getElementById("screenshots-generated").textContent = dashboardData.metrics.processing.screenshotsGenerated || 0;
    document.getElementById("avg-processing-time").textContent = `${dashboardData.metrics.processing.avgProcessingTime || 0}ms`;
    document.getElementById("cache-hit-rate").textContent = `${(dashboardData.metrics.processing.cacheHitRate || 0).toFixed(1)}%`;
    document.getElementById("queue-depth").textContent = dashboardData.metrics.processing.queueDepth || 0;
    // System metrics
    document.getElementById("memory-usage").textContent = `${(dashboardData.metrics.system.memoryUsage?.percentage || 0).toFixed(1)}%`;
    document.getElementById("cpu-usage").textContent = `${(dashboardData.metrics.system.cpuUsage || 0).toFixed(1)}%`;
    document.getElementById("disk-usage").textContent = `${(dashboardData.metrics.system.diskUsage?.percentage || 0).toFixed(1)}%`;
    document.getElementById("active-workers").textContent = dashboardData.metrics.processing.activeWorkers || 0;
  }
  // Load usage overview

  async function loadUsageOverview() {
    try {
      const timeframe = document.getElementById("usage-timeframe").value;
      const response = await fetch(`/dashboard/api/usage-overview?timeframe=${timeframe}`);
      if (!response.ok) throw new Error("Failed to fetch usage data");
      usageData = await response.json();
      updateUsageOverviewUI();
    } catch (error) {
      console.error("Error loading usage data:", error);
      showError("Failed to load usage data");
    }
  }
  // Update usage overview UI

  function updateUsageOverviewUI() {
    const overview = usageData.data.overview;
    document.getElementById("usage-total-requests").textContent = overview.totalRequests;
    document.getElementById("usage-total-errors").textContent = overview.totalErrors;
    document.getElementById("usage-error-rate").textContent = overview.errorRate.toFixed(2);
    document.getElementById("usage-avg-response").textContent = Math.round(overview.avgResponseTime);
    // Update usage table
    const tbody = document.getElementById("usage-table");
    tbody.innerHTML = "";
    usageData.data.apiKeyUsage.forEach(usage => {
      const row = document.createElement("tr");
      row.innerHTML = `
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${usage.apiKey.name}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${usage.apiKey.user}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${usage.totalRequests}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${usage.errorRequests}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${(usage.errorRequests / usage.totalRequests * 100).toFixed(2)}%</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${Math.round(usage.avgResponseTime)}ms</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button onclick="showApiUsageDetails('${usage.apiKey.id}')" class="text-indigo-600 hover:text-indigo-900">View Details</button>
              </td>
          `;
      tbody.appendChild(row);
    });
  }
  // Load error logs

  async function loadErrorLogs() {
    try {
      const level = document.getElementById("error-level").value;
      const params = new URLSearchParams();
      if (level) params.append("level", level);
      const response = await fetch(`/dashboard/api/errors?${params}`);
      if (!response.ok) throw new Error("Failed to fetch error logs");
      errorData = await response.json();
      updateErrorLogsUI();
    } catch (error) {
      console.error("Error loading error logs:", error);
      showError("Failed to load error logs");
    }
  }
  // Update error logs UI

  function updateErrorLogsUI() {
    const stats = errorData.data.stats;
    document.getElementById("error-count").textContent = stats.error || 0;
    document.getElementById("warning-count").textContent = stats.warn || 0;
    document.getElementById("fatal-count").textContent = stats.fatal || 0;
    // Update error logs table
    const tbody = document.getElementById("error-logs-table");
    tbody.innerHTML = "";
    errorData.data.errors.forEach(error => {
      const row = document.createElement("tr");
      const levelClass = error.level === "fatal" ? "text-red-800" : error.level === "error" ? "text-red-600" : "text-yellow-600";
      row.innerHTML = `
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${levelClass}">${error.level.toUpperCase()}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${error.message}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${error.endpoint || "-"}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${error.ipAddress || "-"}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(error.createdAt).toLocaleString()}</td>
          `;
      tbody.appendChild(row);
    });
  }
  // Load API keys

  async function loadApiKeys() {
    try {
      const response = await fetch("/dashboard/api/keys");
      if (!response.ok) throw new Error("Failed to fetch API keys");
      apiKeysData = await response.json();
      updateApiKeysUI();
    } catch (error) {
      console.error("Error loading API keys:", error);
      showError("Failed to load API keys");
    }
  }
  // Update API keys UI

  function updateApiKeysUI() {
    const tbody = document.getElementById("api-keys-table");
    tbody.innerHTML = "";
    apiKeysData.data.forEach(apiKey => {
      const row = document.createElement("tr");
      const statusClass = apiKey.isActive ? "text-green-600" : "text-red-600";
      const statusText = apiKey.isActive ? "Active" : "Inactive";
      row.innerHTML = `
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${apiKey.name}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">${apiKey.key}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${apiKey.rateLimit}/hour</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${statusClass}">${statusText}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(apiKey.createdAt).toLocaleDateString()}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button onclick="toggleApiKey('${apiKey.id}')" class="text-indigo-600 hover:text-indigo-900">
                      ${apiKey.isActive ? "Deactivate" : "Activate"}
                  </button>
                  <button onclick="deleteApiKey('${apiKey.id}')" class="text-red-600 hover:text-red-900">Delete</button>
              </td>
          `;
      tbody.appendChild(row);
    });
  }
  // Load users

  async function loadUsers() {
    try {
      const response = await fetch("/dashboard/api/users");
      if (!response.ok) throw new Error("Failed to fetch users");
      const usersData = await response.json();
      updateUsersUI(usersData);
    } catch (error) {
      console.error("Error loading users:", error);
      showError("Failed to load users");
    }
  }
  // Update users UI

  function updateUsersUI(usersData) {
    const tbody = document.getElementById("users-table");
    tbody.innerHTML = "";
    if (!usersData || !usersData.data || usersData.data.length === 0) {
      tbody.innerHTML = `
                  <tr>
                      <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                          No users found
                      </td>
                  </tr>
              `;
      return;
    }
    usersData.data.forEach(user => {
      const row = document.createElement("tr");
      row.innerHTML = `
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${user.fullName || "N/A"}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.email}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(user.createdAt).toLocaleDateString()}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button onclick="editUser(${user.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                      <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-900">Delete</button>
                  </td>
              `;
      tbody.appendChild(row);
    });
  }
  // User management functions

  function editUser(userId) {
    console.log("Edit user:", userId);
    // TODO: Implement user editing functionality
    showError("User editing not yet implemented");
  }

  async function deleteUser(userId) {
    if (!confirm("Are you sure you want to delete this user? This action cannot be undone.")) {
      return;
    }
    try {
      const response = await fetch(`/dashboard/api/users/${userId}`, {
        method: "DELETE"
      });
      if (!response.ok) throw new Error("Failed to delete user");
      loadUsers(); // Refresh the list
      showError("User deleted successfully", "success");
    } catch (error) {
      console.error("Error deleting user:", error);
      showError("Failed to delete user: " + error.message);
    }
  }
  // Modal functions

  function showCreateApiKeyModal() {
    document.getElementById("create-api-key-modal").classList.remove("hidden");
  }

  function hideCreateApiKeyModal() {
    document.getElementById("create-api-key-modal").classList.add("hidden");
    document.getElementById("create-api-key-form").reset();
  }

  function showNewApiKeyModal(apiKey) {
    document.getElementById("new-api-key-value").textContent = apiKey;
    document.getElementById("new-api-key-modal").classList.remove("hidden");
  }

  function hideNewApiKeyModal() {
    document.getElementById("new-api-key-modal").classList.add("hidden");
    loadApiKeys(); // Refresh the API keys list
  }

  function showNewUserModal() {
    document.getElementById("new-user-modal").classList.remove("hidden");
  }

  function hideNewUserModal() {
    document.getElementById("new-user-modal").classList.add("hidden");
    document.getElementById("create-user-form").reset();
    document.getElementById("user-creation-success").classList.add("hidden");
  }
  // API functions

  async function createApiKey(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const data = {
      name: formData.get("name"),
      rateLimit: parseInt(formData.get("rateLimit"))
    };
    try {
      const response = await fetch("/dashboard/api/keys", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error("Failed to create API key");
      const result = await response.json();
      hideCreateApiKeyModal();
      showNewApiKeyModal(result.data.key);
    } catch (error) {
      console.error("Error creating API key:", error);
      showError("Failed to create API key");
    }
  }

  async function toggleApiKey(id) {
    try {
      const response = await fetch(`/dashboard/api/keys/${id}/toggle`, {
        method: "PATCH"
      });
      if (!response.ok) throw new Error("Failed to toggle API key");
      loadApiKeys(); // Refresh the list
    } catch (error) {
      console.error("Error toggling API key:", error);
      showError("Failed to toggle API key");
    }
  }

  async function deleteApiKey(id) {
    if (!confirm("Are you sure you want to delete this API key? This action cannot be undone.")) {
      return;
    }
    try {
      const response = await fetch(`/dashboard/api/keys/${id}`, {
        method: "DELETE"
      });
      if (!response.ok) throw new Error("Failed to delete API key");
      loadApiKeys(); // Refresh the list
    } catch (error) {
      console.error("Error deleting API key:", error);
      showError("Failed to delete API key");
    }
  }

  async function createUser(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const data = {
      fullName: formData.get("fullName"),
      email: formData.get("email"),
      password: formData.get("password")
    };
    try {
      // Note: This endpoint doesn't exist in the original controller
      // You may need to implement it
      const response = await fetch("/dashboard/api/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error("Failed to create user");
      document.getElementById("user-creation-success").classList.remove("hidden");
      event.target.reset();
      loadUsers(); // Refresh the users list
      setTimeout(() => {
        hideNewUserModal();
      }, 2e3);
    } catch (error) {
      console.error("Error creating user:", error);
      showError("Failed to create user");
    }
  }

  async function testErrorLog() {
    try {
      const response = await fetch("/dashboard/api/errors", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          level: "error",
          message: "Test error from dashboard",
          context: {
            source: "dashboard",
            test: true
          }
        })
      });
      if (!response.ok) throw new Error("Failed to create test error");
      // Refresh error logs after a short delay
      setTimeout(loadErrorLogs, 1e3);
    } catch (error) {
      console.error("Error creating test error:", error);
      showError("Failed to create test error");
    }
  }
  // Utility functions

  function showError(message, type = "error") {
    const errorElement = document.getElementById("error-message");
    const textElement = document.getElementById("error-text");
    textElement.textContent = message;
    // Update styling based on type
    if (type === "success") {
      errorElement.className = "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4";
    } else {
      errorElement.className = "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4";
    }
    errorElement.classList.remove("hidden");
    setTimeout(() => {
      errorElement.classList.add("hidden");
    }, 5e3);
  }

  async function showApiUsageDetails(apiKeyId) {
    // Show the modal
    document.getElementById("api-usage-details-modal").classList.remove("hidden");

    // Show loading state
    document.getElementById("usage-details-loading").classList.remove("hidden");
    document.getElementById("usage-details-content").classList.add("hidden");
    document.getElementById("usage-details-error").classList.add("hidden");

    try {
      // Get current timeframe from the main usage overview
      const timeframe = document.getElementById("usage-timeframe").value;

      // Fetch detailed usage data
      const response = await fetch(`/dashboard/api/usage/${apiKeyId}?timeframe=${timeframe}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // Hide loading and show content
      document.getElementById("usage-details-loading").classList.add("hidden");
      document.getElementById("usage-details-content").classList.remove("hidden");

      // Populate the modal with data
      populateUsageDetailsModal(result.data);

    } catch (error) {
      console.error("Error loading API usage details:", error);

      // Hide loading and show error
      document.getElementById("usage-details-loading").classList.add("hidden");
      document.getElementById("usage-details-error").classList.remove("hidden");
      document.getElementById("usage-details-error-message").textContent =
        error.message || "Failed to load usage details";
    }
  }

  function populateUsageDetailsModal(data) {
    // Populate API key information
    document.getElementById("details-api-key-name").textContent = data.apiKey.name;
    document.getElementById("details-api-key-user").textContent = data.apiKey.user;
    document.getElementById("details-api-key-rate-limit").textContent = `${data.apiKey.rateLimit} req/hour`;

    // Populate statistics
    document.getElementById("details-total-requests").textContent = data.stats.totalRequests;
    document.getElementById("details-successful-requests").textContent = data.stats.successfulRequests;
    document.getElementById("details-error-requests").textContent = data.stats.errorRequests;
    document.getElementById("details-avg-response-time").textContent = `${Math.round(data.stats.avgResponseTime)}ms`;

    // Populate endpoint statistics
    const endpointStatsBody = document.getElementById("details-endpoint-stats");
    endpointStatsBody.innerHTML = "";

    const endpointStats = data.stats.endpointStats || {};
    const endpointEntries = Object.entries(endpointStats);

    if (endpointEntries.length === 0) {
      const row = document.createElement("tr");
      row.innerHTML = `
        <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No endpoint usage data available</td>
      `;
      endpointStatsBody.appendChild(row);
    } else {
      endpointEntries.forEach(([endpoint, stats]) => {
        const row = document.createElement("tr");
        const errorRate = stats.totalRequests > 0 ? (stats.errorRequests / stats.totalRequests * 100).toFixed(2) : "0.00";

        row.innerHTML = `
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${endpoint}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${stats.totalRequests}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${stats.errorRequests}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${errorRate}%</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${Math.round(stats.avgResponseTime)}ms</td>
        `;
        endpointStatsBody.appendChild(row);
      });
    }

    // Populate recent usage
    const recentUsageBody = document.getElementById("details-recent-usage");
    recentUsageBody.innerHTML = "";

    const recentUsage = data.recentUsage || [];

    if (recentUsage.length === 0) {
      const row = document.createElement("tr");
      row.innerHTML = `
        <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No recent usage data available</td>
      `;
      recentUsageBody.appendChild(row);
    } else {
      recentUsage.forEach(usage => {
        const row = document.createElement("tr");
        const statusClass = usage.statusCode >= 200 && usage.statusCode < 300 ? "text-green-600" : "text-red-600";
        const formattedTime = new Date(usage.createdAt).toLocaleString();

        row.innerHTML = `
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formattedTime}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${usage.method}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${usage.endpoint}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${statusClass}">${usage.statusCode}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${usage.responseTime}ms</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${usage.ipAddress || '-'}</td>
        `;
        recentUsageBody.appendChild(row);
      });
    }
  }

  function hideApiUsageDetailsModal() {
    document.getElementById("api-usage-details-modal").classList.add("hidden");
  }

  // Add event listener for clicking outside the modal to close it
  document.addEventListener("DOMContentLoaded", function () {
    const modal = document.getElementById("api-usage-details-modal");
    if (modal) {
      modal.addEventListener("click", function (event) {
        if (event.target === modal) {
          hideApiUsageDetailsModal();
        }
      });
    }
  });
  // Load current user information

  async function loadCurrentUser() {
    try {
      const response = await fetch("/dashboard/api/user");
      if (!response.ok) throw new Error("Failed to fetch user data");
      const result = await response.json();
      const user = result.data;
      // Update user info in header
      document.getElementById("user-name").textContent = user.fullName || "Unknown User";
      document.getElementById("user-email").textContent = user.email;
      // Update user initials
      const initials = user.fullName ? user.fullName.split(" ").map(n => n[0]).join("").toUpperCase() : user.email[0].toUpperCase();
      document.getElementById("user-initials").textContent = initials;
    } catch (error) {
      console.error("Error loading user data:", error);
      document.getElementById("user-name").textContent = "Error loading user";
      document.getElementById("user-email").textContent = "";
    }
  }
  // Show change password modal

  function showChangePasswordModal() {
    document.getElementById("change-password-modal").classList.remove("hidden");
    document.getElementById("change-password-form").reset();
    document.getElementById("password-change-success").classList.add("hidden");
    document.getElementById("password-change-error").classList.add("hidden");
  }
  // Hide change password modal

  function hideChangePasswordModal() {
    document.getElementById("change-password-modal").classList.add("hidden");
    document.getElementById("change-password-form").reset();
    document.getElementById("password-change-success").classList.add("hidden");
    document.getElementById("password-change-error").classList.add("hidden");
  }
  // Handle password change form submission

  async function changePassword(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const newPassword = formData.get("newPassword");
    const confirmPassword = formData.get("confirmPassword");
    // Client-side validation
    if (newPassword !== confirmPassword) {
      showPasswordError("New passwords do not match");
      return;
    }
    if (newPassword.length < 6) {
      showPasswordError("Password must be at least 6 characters long");
      return;
    }
    const passwordData = {
      currentPassword: formData.get("currentPassword"),
      newPassword: newPassword,
      confirmPassword: confirmPassword
    };
    try {
      const response = await fetch("/dashboard/api/change-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(passwordData)
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.detail?.message || "Failed to change password");
      }
      // Show success message
      document.getElementById("password-change-success").classList.remove("hidden");
      document.getElementById("password-change-error").classList.add("hidden");
      document.getElementById("change-password-form").reset();
      // Auto-close modal after 2 seconds
      setTimeout(() => {
        hideChangePasswordModal();
      }, 2e3);
    } catch (error) {
      showPasswordError(error.message);
    }
  }
  // Show password error message

  function showPasswordError(message) {
    document.getElementById("password-error-text").textContent = message;
    document.getElementById("password-change-error").classList.remove("hidden");
    document.getElementById("password-change-success").classList.add("hidden");
  }

  // Navigation highlighting
  function updateNavigationHighlight() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('nav a');

    navLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href === currentPath || (currentPath.startsWith('/dashboard') && href === '/dashboard')) {
        link.classList.remove('text-gray-500', 'border-transparent');
        link.classList.add('text-gray-900', 'border-blue-600');
      } else {
        link.classList.remove('text-gray-900', 'border-blue-600');
        link.classList.add('text-gray-500', 'border-transparent');
      }
    });
  }

  // Update navigation on page load
  document.addEventListener('DOMContentLoaded', updateNavigationHighlight);
</script>